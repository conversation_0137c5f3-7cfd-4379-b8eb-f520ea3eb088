import type { Plugin } from 'vite';
import { resolve } from 'path';

export function configJeecgOnlinePlugin(): Plugin {
  return {
    name: 'vite:jeecg-online',
    enforce: 'pre',
    resolveId(id, importer) {
      // 处理 @jeecg/online 包中的路径别名
      if (id.startsWith('/@/') || id.startsWith('@/')) {
        const realPath = resolve(process.cwd(), 'src', id.replace(/^[@/]+\//, ''));
        return realPath;
      }
      // 处理直接引用 src 目录下的文件
      if (id.startsWith('src/')) {
        return resolve(process.cwd(), id);
      }
      return null;
    },
    transform(code, id) {
      // 如果是 @jeecg/online 包中的文件
      if (id.includes('node_modules/@jeecg/online')) {
        // 替换所有的 /@/ 和 @/ 引用为相对路径
        code = code.replace(/['"][@/]+\//g, '"src/');
        return {
          code,
          map: null
        };
      }
      return null;
    }
  };
} 
